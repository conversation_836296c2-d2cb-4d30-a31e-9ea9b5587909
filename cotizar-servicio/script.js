document.addEventListener('DOMContentLoaded', () => {
  // Back to Top Button functionality
  const backToTopButton = document.getElementById('back-to-top');

  // Show/hide back to top button based on scroll position
  window.addEventListener('scroll', function() {
    if (window.pageYOffset > 300) {
      backToTopButton.classList.add('visible');
    } else {
      backToTopButton.classList.remove('visible');
    }
  });

  // Scroll to top when button is clicked
  backToTopButton.addEventListener('click', function() {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  });

  // Mobile Menu Animation
  const menuBtn = document.querySelector('.header-menuImg');
  const navList = document.querySelector('.header-nav');
  const body = document.querySelector('body');

  if (menuBtn) {
    menuBtn.addEventListener('click', function() {
      body.classList.toggle('menu-open');
      navList.classList.toggle('header-nav__active');
      body.classList.toggle('stop-scroll');
    });
  }

  // Close menu when clicking on links
  if (navList) {
    navList.addEventListener('click', (event) => {
      if (event.target.classList.contains('header-nav__link')) {
        navList.classList.remove('header-nav__active');
        body.classList.remove('stop-scroll');
        body.classList.remove('menu-open');
      }
    });
  }

  // Close menu when clicking outside
  document.addEventListener('click', function(event) {
    if (navList && navList.classList.contains('header-nav__active') &&
        !navList.contains(event.target) &&
        !menuBtn.contains(event.target)) {
      navList.classList.remove('header-nav__active');
      body.classList.remove('stop-scroll');
      body.classList.remove('menu-open');
    }
  });

  // Form functionality starts here
  const form = document.getElementById('quoteForm');
  const submitBtn = document.getElementById('submitForm');
  const disclaimerSection = document.getElementById('disclaimerSection');
  const acceptDisclaimer = document.getElementById('acceptDisclaimer');
  const resultMessage = document.getElementById('result-message');

  // Form fields
  const fields = {
    empresa: document.getElementById('empresa'),
    nombre: document.getElementById('nombre'),
    correo: document.getElementById('correo'),
    direccion_origen: document.getElementById('direccion_origen'),
    cp_origen: document.getElementById('cp_origen'),
    direccion_destino: document.getElementById('direccion_destino'),
    cp_destino: document.getElementById('cp_destino'),
    tipo_mercancia: document.getElementById('tipo_mercancia'),
    piezas: document.getElementById('piezas'),
    peso: document.getElementById('peso'),
    dimensiones: document.getElementById('dimensiones'),
    descripcion_mercancia: document.getElementById('descripcion_mercancia')
  };

  // Track validation state for each field
  const validationState = {};
  Object.keys(fields).forEach(key => {
    validationState[key] = false;
  });

  // Add comprehensive event listeners for real-time validation
  Object.entries(fields).forEach(([key, field]) => {
    // Real-time validation on input
    field.addEventListener('input', () => {
      // Debounce validation for better performance
      clearTimeout(field.validationTimeout);
      field.validationTimeout = setTimeout(() => {
        validateField(field, key);
        updateFormState();
      }, 300);
    });

    // Immediate validation on blur
    field.addEventListener('blur', () => {
      clearTimeout(field.validationTimeout);
      validateField(field, key);
      updateFormState();
    });

    // Special handling for select elements
    if (field.tagName === 'SELECT') {
      field.addEventListener('change', () => {
        validateField(field, key);
        updateFormState();
      });
    }

    // Add input formatting helpers
    if (key === 'cp_origen' || key === 'cp_destino') {
      field.addEventListener('input', (e) => {
        // Only allow numbers and limit to 5 digits
        e.target.value = e.target.value.replace(/\D/g, '').substring(0, 5);
      });
    }

    if (key === 'piezas') {
      field.addEventListener('input', (e) => {
        // Only allow positive integers
        e.target.value = e.target.value.replace(/\D/g, '');
      });
    }

    if (key === 'peso') {
      field.addEventListener('input', (e) => {
        // Allow numbers with up to 2 decimal places
        let value = e.target.value.replace(/[^\d.]/g, '');
        const parts = value.split('.');
        if (parts.length > 2) {
          value = parts[0] + '.' + parts.slice(1).join('');
        }
        if (parts[1] && parts[1].length > 2) {
          value = parts[0] + '.' + parts[1].substring(0, 2);
        }
        e.target.value = value;
      });
    }

    if (key === 'dimensiones') {
      field.addEventListener('input', (e) => {
        // Auto-format dimensions as user types
        let value = e.target.value.replace(/[^\d\sx×X]/g, '');
        // Replace multiple spaces with single space
        value = value.replace(/\s+/g, ' ');
        // Auto-add 'x' between numbers
        value = value.replace(/(\d)\s+(\d)/g, '$1 x $2');
        e.target.value = value;
      });
    }
  });

  // Enable/disable submit button based on disclaimer acceptance and form validation
  acceptDisclaimer.addEventListener('change', () => {
    updateSubmitButtonState();
  });

  // Handle form submission
  form.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    if (!validateAllFields() || !acceptDisclaimer.checked) {
      showMessage('Por favor, complete todos los campos correctamente y acepte el aviso.', 'error');
      return;
    }

    // Disable submit button and show loading state
    submitBtn.disabled = true;
    submitBtn.textContent = 'Enviando...';

    try {
      const formData = new FormData();
      Object.entries(fields).forEach(([key, field]) => {
        formData.append(key, field.value.trim());
      });

      const response = await fetch('process_quote.php', {
        method: 'POST',
        body: formData
      });

      // Check if response is ok and has content
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Get response text first to check if it's valid JSON
      const responseText = await response.text();

      if (!responseText.trim()) {
        throw new Error('Empty response from server');
      }

      let result;
      try {
        result = JSON.parse(responseText);
      } catch (jsonError) {
        console.error('JSON parsing error:', jsonError);
        console.error('Response text:', responseText);
        throw new Error('Invalid response format from server');
      }

      if (result.success) {
        showMessage('Su solicitud de cotización ha sido enviada exitosamente. Nos pondremos en contacto con usted pronto.', 'success');

        // Reset form and states
        form.reset();
        resetFormState();
        acceptDisclaimer.checked = false;
      } else {
        showMessage(result.message || 'Hubo un error al enviar su solicitud. Por favor, intente nuevamente.', 'error');
      }
    } catch (error) {
      console.error('Error:', error);

      // Provide more specific error messages based on error type
      let errorMessage = 'Hubo un error al enviar su solicitud. Por favor, intente nuevamente.';

      if (error.message.includes('HTTP error')) {
        errorMessage = 'Error del servidor. Por favor, intente nuevamente más tarde.';
      } else if (error.message.includes('Invalid response format')) {
        errorMessage = 'Error de comunicación con el servidor. Por favor, intente nuevamente.';
      } else if (error.message.includes('Empty response')) {
        errorMessage = 'No se recibió respuesta del servidor. Por favor, intente nuevamente.';
      }

      showMessage(errorMessage, 'error');
    } finally {
      submitBtn.disabled = false;
      submitBtn.textContent = 'Enviar Solicitud';
    }
  });

  // Enhanced validation functions
  function validateField(field, fieldKey) {
    const value = field.value.trim();
    const errorSpan = document.getElementById(`error-${field.id}`);
    let errorMessage = '';
    let isValid = true;

    // Clear previous states
    field.classList.remove('error-input', 'valid-input');
    errorSpan.style.display = 'none';
    errorSpan.textContent = '';

    // Skip validation if field is empty and not required for real-time validation
    if (!value && !field.hasAttribute('required')) {
      validationState[fieldKey] = true;
      return true;
    }

    // Comprehensive validation rules
    switch (field.id) {
      case 'empresa':
        if (!value) {
          errorMessage = 'El nombre de la empresa es obligatorio.';
          isValid = false;
        } else if (value.length < 2) {
          errorMessage = 'El nombre debe tener al menos 2 caracteres.';
          isValid = false;
        } else if (value.length > 100) {
          errorMessage = 'El nombre no puede exceder 100 caracteres.';
          isValid = false;
        } else if (!/^[\w\sáéíóúÁÉÍÓÚñÑ\.\-&(),]{2,}$/u.test(value)) {
          errorMessage = 'Solo se permiten letras, números y símbolos básicos (.,&-()).';
          isValid = false;
        }
        break;

      case 'nombre':
        if (!value) {
          errorMessage = 'El nombre del contacto es obligatorio.';
          isValid = false;
        } else if (value.length < 2) {
          errorMessage = 'El nombre debe tener al menos 2 caracteres.';
          isValid = false;
        } else if (value.length > 80) {
          errorMessage = 'El nombre no puede exceder 80 caracteres.';
          isValid = false;
        } else if (!/^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s\-\.]{2,}$/u.test(value)) {
          errorMessage = 'Solo se permiten letras, espacios, guiones y puntos.';
          isValid = false;
        }
        break;

      case 'correo':
        if (!value) {
          errorMessage = 'El correo electrónico es obligatorio.';
          isValid = false;
        } else if (value.length > 254) {
          errorMessage = 'El correo electrónico es demasiado largo.';
          isValid = false;
        } else if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/i.test(value)) {
          errorMessage = 'Ingrese un correo electrónico válido.';
          isValid = false;
        } else {
          // Additional email validation
          const emailParts = value.split('@');
          if (emailParts[0].length > 64) {
            errorMessage = 'La parte local del correo es demasiado larga.';
            isValid = false;
          } else if (emailParts[1].length > 253) {
            errorMessage = 'El dominio del correo es demasiado largo.';
            isValid = false;
          }
        }
        break;

      case 'direccion_origen':
      case 'direccion_destino':
        const direccionLabel = field.id === 'direccion_origen' ? 'origen' : 'destino';
        if (!value) {
          errorMessage = `La dirección de ${direccionLabel} es obligatoria.`;
          isValid = false;
        } else if (value.length < 15) {
          errorMessage = `La dirección debe ser más específica (mínimo 15 caracteres).`;
          isValid = false;
        } else if (value.length > 200) {
          errorMessage = 'La dirección no puede exceder 200 caracteres.';
          isValid = false;
        } else if (!/^[a-zA-ZáéíóúÁÉÍÓÚñÑ0-9\s\-.#,°]+$/u.test(value)) {
          errorMessage = 'La dirección contiene caracteres no válidos.';
          isValid = false;
        }
        break;

      case 'cp_origen':
      case 'cp_destino':
        const cpLabel = field.id === 'cp_origen' ? 'origen' : 'destino';
        if (!value) {
          errorMessage = `El código postal de ${cpLabel} es obligatorio.`;
          isValid = false;
        } else if (!/^\d{5}$/.test(value)) {
          errorMessage = 'El código postal debe tener exactamente 5 dígitos.';
          isValid = false;
        } else if (parseInt(value) < 1000 || parseInt(value) > 99999) {
          errorMessage = 'Ingrese un código postal válido de México.';
          isValid = false;
        }
        break;

      case 'tipo_mercancia':
        const validTypes = ['sobredimensionado', 'hazmat', 'perecedero', 'carga_general'];
        if (!value) {
          errorMessage = 'Debe seleccionar un tipo de mercancía.';
          isValid = false;
        } else if (!validTypes.includes(value)) {
          errorMessage = 'Seleccione una opción válida.';
          isValid = false;
        }
        break;

      case 'piezas':
        if (!value) {
          errorMessage = 'El número de piezas es obligatorio.';
          isValid = false;
        } else if (!/^\d+$/.test(value)) {
          errorMessage = 'Solo se permiten números enteros.';
          isValid = false;
        } else if (parseInt(value) < 1) {
          errorMessage = 'Debe ser al menos 1 pieza.';
          isValid = false;
        } else if (parseInt(value) > 10000) {
          errorMessage = 'El número de piezas no puede exceder 10,000.';
          isValid = false;
        }
        break;

      case 'peso':
        if (!value) {
          errorMessage = 'El peso es obligatorio.';
          isValid = false;
        } else if (!/^\d+(\.\d{1,2})?$/.test(value)) {
          errorMessage = 'Ingrese un peso válido (ej: 25.5).';
          isValid = false;
        } else if (parseFloat(value) <= 0) {
          errorMessage = 'El peso debe ser mayor a 0.';
          isValid = false;
        } else if (parseFloat(value) > 50000) {
          errorMessage = 'El peso no puede exceder 50,000 kg.';
          isValid = false;
        }
        break;

      case 'dimensiones':
        if (!value) {
          errorMessage = 'Las dimensiones son obligatorias.';
          isValid = false;
        } else if (!/^\d+\s*[xX×]\s*\d+\s*[xX×]\s*\d+(\s*(cm|CM)?)?$/i.test(value)) {
          errorMessage = 'Formato: Largo x Ancho x Alto (ej: 100 x 50 x 30).';
          isValid = false;
        } else {
          // Extract dimensions and validate ranges
          const matches = value.match(/(\d+)\s*[xX×]\s*(\d+)\s*[xX×]\s*(\d+)/);
          if (matches) {
            const [, largo, ancho, alto] = matches.map(Number);
            if (largo > 2000 || ancho > 2000 || alto > 2000) {
              errorMessage = 'Las dimensiones no pueden exceder 2000 cm en ninguna medida.';
              isValid = false;
            } else if (largo < 1 || ancho < 1 || alto < 1) {
              errorMessage = 'Todas las dimensiones deben ser mayores a 0.';
              isValid = false;
            }
          }
        }
        break;

      case 'descripcion_mercancia':
        if (!value) {
          errorMessage = 'La descripción de la mercancía es obligatoria.';
          isValid = false;
        } else if (value.length < 10) {
          errorMessage = 'La descripción debe tener al menos 10 caracteres.';
          isValid = false;
        } else if (value.length > 500) {
          errorMessage = 'La descripción no puede exceder 500 caracteres.';
          isValid = false;
        } else if (!/^[a-zA-ZáéíóúÁÉÍÓÚñÑ0-9\s\-.,;:()\/&+%$#@!]+$/u.test(value)) {
          errorMessage = 'La descripción contiene caracteres no válidos.';
          isValid = false;
        }
        break;
    }

    // Update validation state
    validationState[fieldKey] = isValid;

    // Apply visual feedback
    if (!isValid) {
      field.classList.add('error-input');
      errorSpan.textContent = errorMessage;
      errorSpan.style.display = 'block';
    } else {
      field.classList.add('valid-input');
    }

    return isValid;
  }

  // Validate all fields with optional force parameter
  function validateAllFields(forceValidation = false) {
    let isValid = true;

    Object.entries(fields).forEach(([key, field]) => {
      const fieldValid = validateField(field, key);
      if (!fieldValid) {
        isValid = false;
      }
    });

    return isValid;
  }

  // Update form state and button availability
  function updateFormState() {
    updateSubmitButtonState();
  }

  // Update submit button state based on form validation and disclaimer acceptance
  function updateSubmitButtonState() {
    const allValid = Object.values(validationState).every(state => state === true);
    const disclaimerAccepted = acceptDisclaimer.checked;

    // Update button state
    if (allValid && disclaimerAccepted) {
      submitBtn.disabled = false;
      submitBtn.classList.remove('btn-disabled');
    } else {
      submitBtn.disabled = true;
      submitBtn.classList.add('btn-disabled');
    }
  }

  // Scroll to first error field
  function scrollToFirstError() {
    const firstErrorField = Object.entries(validationState).find(([key, isValid]) => !isValid);
    if (firstErrorField) {
      const fieldElement = fields[firstErrorField[0]];
      fieldElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      fieldElement.focus();
    }
  }

  // Enhanced form state management
  function resetFormState() {
    Object.keys(validationState).forEach(key => {
      validationState[key] = false;
    });

    Object.values(fields).forEach(field => {
      field.classList.remove('error-input', 'valid-input');
      const errorSpan = document.getElementById(`error-${field.id}`);
      if (errorSpan) {
        errorSpan.style.display = 'none';
        errorSpan.textContent = '';
      }
    });

    updateFormState();
  }

  function showMessage(message, type) {
    resultMessage.textContent = message;
    resultMessage.className = `result-message ${type}`;
    resultMessage.style.display = 'block';
    
    // Scroll to message
    resultMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // Hide message after 10 seconds for success, keep error messages visible
    if (type === 'success') {
      setTimeout(() => {
        resultMessage.style.display = 'none';
      }, 10000);
    }
  }

  // Multi-step wizard functionality
  const wizard = {
    currentStep: 1,
    totalSteps: 4,
    steps: [
      {
        id: 1,
        title: 'Información General',
        fields: ['empresa', 'nombre', 'correo']
      },
      {
        id: 2,
        title: 'Origen y Destino',
        fields: ['direccion_origen', 'cp_origen', 'direccion_destino', 'cp_destino']
      },
      {
        id: 3,
        title: 'Información de la Carga',
        fields: ['tipo_mercancia', 'piezas', 'peso', 'dimensiones', 'descripcion_mercancia']
      },
      {
        id: 4,
        title: 'Revisión y Confirmación',
        fields: []
      }
    ]
  };

  // Initialize wizard
  function initializeWizard() {
    createWizardNavigation();
    createProgressIndicator();
    showStep(1);
    updateWizardState();
  }

  // Create wizard navigation
  function createWizardNavigation() {
    const form = document.getElementById('quoteForm');

    // Create navigation container
    const wizardNav = document.createElement('div');
    wizardNav.className = 'wizard-navigation';
    wizardNav.innerHTML = `
      <button type="button" id="prevStep" class="btn-wizard btn-prev" disabled>
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="15,18 9,12 15,6"></polyline>
        </svg>
        Anterior
      </button>
      <button type="button" id="nextStep" class="btn-wizard btn-next">
        Siguiente
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="9,18 15,12 9,6"></polyline>
        </svg>
      </button>
    `;

    // Insert navigation at the end of the form
    form.appendChild(wizardNav);

    // Add event listeners
    document.getElementById('prevStep').addEventListener('click', () => previousStep());
    document.getElementById('nextStep').addEventListener('click', () => nextStep());
  }

  // Create progress indicator
  function createProgressIndicator() {
    const form = document.getElementById('quoteForm');

    const progressContainer = document.createElement('div');
    progressContainer.className = 'wizard-progress';
    progressContainer.innerHTML = `
      <div class="progress-steps">
        ${wizard.steps.map((step) => `
          <div class="progress-step" data-step="${step.id}">
            <div class="step-number">${step.id}</div>
            <div class="step-title">${step.title}</div>
          </div>
        `).join('')}
      </div>
      <div class="progress-bar">
        <div class="progress-fill"></div>
      </div>
    `;

    // Insert at the beginning of the form
    form.insertBefore(progressContainer, form.firstChild);
  }

  // Show specific step
  function showStep(stepNumber) {
    wizard.currentStep = stepNumber;

    // Hide all form sections with smooth transition
    document.querySelectorAll('.form-section').forEach((section) => {
      section.classList.remove('wizard-visible');
      section.classList.add('wizard-hidden');
    });

    // Show current step section after a brief delay for smooth transition
    setTimeout(() => {
      const sections = document.querySelectorAll('.form-section');
      if (sections[stepNumber - 1]) {
        sections[stepNumber - 1].classList.remove('wizard-hidden');
        sections[stepNumber - 1].classList.add('wizard-visible');
      }
    }, 100);

    // Update progress indicator
    updateProgressIndicator();

    // Update navigation buttons
    updateNavigationButtons();

    // Show/hide submit button and update review data
    const formActions = document.querySelector('.form-actions');

    if (stepNumber === wizard.totalSteps) {
      formActions.style.display = 'block';
      updateReviewData();
    } else {
      formActions.style.display = 'none';
    }

    // Scroll to top of form
    document.querySelector('.quote-container').scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  }

  // Update progress indicator
  function updateProgressIndicator() {
    const progressSteps = document.querySelectorAll('.progress-step');
    const progressFill = document.querySelector('.progress-fill');

    progressSteps.forEach((step, index) => {
      const stepNumber = index + 1;
      step.classList.remove('active', 'completed');

      if (stepNumber < wizard.currentStep) {
        step.classList.add('completed');
      } else if (stepNumber === wizard.currentStep) {
        step.classList.add('active');
      }
    });

    // Update progress bar
    const progressPercentage = ((wizard.currentStep - 1) / (wizard.totalSteps - 1)) * 100;
    if (progressFill) {
      progressFill.style.width = `${progressPercentage}%`;
    }
  }

  // Update navigation buttons
  function updateNavigationButtons() {
    const prevBtn = document.getElementById('prevStep');
    const nextBtn = document.getElementById('nextStep');

    if (prevBtn) {
      prevBtn.disabled = wizard.currentStep === 1;
    }

    if (nextBtn) {
      if (wizard.currentStep === wizard.totalSteps) {
        nextBtn.style.display = 'none';
      } else if (wizard.currentStep === wizard.totalSteps - 1) {
        nextBtn.style.display = 'flex';
        nextBtn.innerHTML = `
          Revisar
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="9,18 15,12 9,6"></polyline>
          </svg>
        `;
      } else {
        nextBtn.style.display = 'flex';
        nextBtn.innerHTML = `
          Siguiente
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="9,18 15,12 9,6"></polyline>
          </svg>
        `;
      }
    }
  }

  // Validate current step
  function validateCurrentStep() {
    const currentStepData = wizard.steps[wizard.currentStep - 1];

    // Review step doesn't need field validation, just check if all previous steps are valid
    if (wizard.currentStep === wizard.totalSteps) {
      return Object.values(validationState).every(state => state === true);
    }

    let isValid = true;

    currentStepData.fields.forEach(fieldKey => {
      const field = fields[fieldKey];
      if (field && !validateField(field, fieldKey)) {
        isValid = false;
      }
    });

    return isValid;
  }

  // Next step
  function nextStep() {
    if (!validateCurrentStep()) {
      // Scroll to first error in current step
      const currentStepData = wizard.steps[wizard.currentStep - 1];
      const firstErrorField = currentStepData.fields.find(fieldKey =>
        !validationState[fieldKey]
      );

      if (firstErrorField) {
        const fieldElement = fields[firstErrorField];
        fieldElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        fieldElement.focus();
      }

      return;
    }

    if (wizard.currentStep < wizard.totalSteps) {
      showStep(wizard.currentStep + 1);
    }
  }

  // Previous step
  function previousStep() {
    if (wizard.currentStep > 1) {
      showStep(wizard.currentStep - 1);
    }
  }

  // Update wizard state
  function updateWizardState() {
    updateNavigationButtons();

    // Update next button state based on current step validation
    const nextBtn = document.getElementById('nextStep');
    if (nextBtn && wizard.currentStep < wizard.totalSteps) {
      const isCurrentStepValid = validateCurrentStep();
      nextBtn.disabled = !isCurrentStepValid;

      if (isCurrentStepValid) {
        nextBtn.classList.remove('btn-disabled');
      } else {
        nextBtn.classList.add('btn-disabled');
      }
    }
  }

  // Update review data
  function updateReviewData() {
    const reviewFields = [
      'empresa', 'nombre', 'correo',
      'direccion_origen', 'cp_origen', 'direccion_destino', 'cp_destino',
      'tipo_mercancia', 'piezas', 'peso', 'dimensiones', 'descripcion_mercancia'
    ];

    reviewFields.forEach(fieldName => {
      const field = fields[fieldName];
      const reviewElement = document.getElementById(`review-${fieldName}`);

      if (field && reviewElement) {
        let value = field.value.trim();

        // Format specific fields
        if (fieldName === 'tipo_mercancia' && value) {
          const options = {
            'sobredimensionado': 'Sobredimensionado',
            'hazmat': 'Hazmat (Materiales Peligrosos)',
            'perecedero': 'Perecedero',
            'carga_general': 'Carga General'
          };
          value = options[value] || value;
        } else if (fieldName === 'peso' && value) {
          value = `${value} kg`;
        } else if (fieldName === 'piezas' && value) {
          value = `${value} ${parseInt(value) === 1 ? 'pieza' : 'piezas'}`;
        }

        if (value) {
          reviewElement.textContent = value;
          reviewElement.classList.remove('empty');
        } else {
          reviewElement.textContent = 'No especificado';
          reviewElement.classList.add('empty');
        }
      }
    });
  }

  // Navigate to specific step (for edit buttons)
  window.goToStep = function(stepNumber) {
    if (stepNumber >= 1 && stepNumber <= wizard.totalSteps) {
      showStep(stepNumber);
    }
  };

  // Override the original updateFormState to work with wizard
  const originalUpdateFormState = updateFormState;
  updateFormState = function() {
    originalUpdateFormState();
    updateWizardState();
  };

  // Initialize wizard after DOM is ready
  initializeWizard();

  // Initialize form state on page load
  updateFormState();

  // Initialize submit button as disabled
  submitBtn.disabled = true;
  submitBtn.classList.add('btn-disabled');
});
