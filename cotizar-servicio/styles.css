/* Reset and Base Styles */
*,
*::after,
*::before {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Outfit', sans-serif;
  background-color: #f4f4f4;
  line-height: 1.6;
  color: #333;
}

a {
  text-decoration: none;
}

ul, li {
  list-style: none;
  margin: 0;
  padding: 0;
}

/* Main site container for navbar */
.container {
  margin: 0 auto;
  max-width: 1680px;
  padding: 0 20px;
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #000F47;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 999;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  background-color: #00FF00;
  color: #000F47;
  transform: translateY(-3px);
}

/* Header and Navigation Styles */
.header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  padding: 20px 0;
}

.header-items {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-logo img {
  width: 140px;
}

.header-menuImg {
  display: none;
  cursor: pointer;
}

.header-nav {
  display: flex;
  align-items: center;
  transition: left 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
}

.header-nav__list {
  margin-right: 40px;
}

.header-nav__list:last-child {
  margin-right: 0;
}

.header-nav__link {
  font-size: 20px;
  font-weight: 400;
  color: #000E49;
  transition: all 0.5s ease-in-out;
  display: inline-block;
}

.header-nav__link:hover {
  transform: scale(1.1);
}

.header-btn {
  font-size: 20px;
  font-weight: 800;
  color: #FFFFFF;
  padding: 15px 35px;
  background-color: #000F47;
  transition: all 0.5s ease-in-out;
}

.header-btn:hover {
  transition: all 0.5s ease-in-out;
  opacity: 0.8;
}

/* Quote form container - renamed to avoid conflict with main site container */
.quote-container {
  max-width: 800px;
  margin: 40px auto 20px;
  background: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Typography */
h1 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #000188;
  font-size: 2.2em;
  text-align: center;
}

.subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 30px;
  font-size: 1.1em;
}

h2 {
  color: #000188;
  border-bottom: 2px solid #000188;
  padding-bottom: 5px;
  margin-top: 30px;
  margin-bottom: 20px;
  font-size: 1.4em;
}

h3 {
  color: #000188;
  margin-bottom: 10px;
}

/* Form Sections */
.form-section {
  margin-bottom: 30px;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.form-col {
  flex: 1;
}

/* Form Elements */
label {
  display: block;
  margin: 15px 0 5px;
  font-weight: bold;
  color: #333;
}

input[type="text"],
input[type="number"],
input[type="email"],
select,
textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: #000188;
  box-shadow: 0 0 5px rgba(0, 1, 136, 0.3);
}

textarea {
  resize: vertical;
  min-height: 100px;
}

select {
  cursor: pointer;
}

/* Validation Styles */
.error-input {
  border-color: #dc3545 !important;
  box-shadow: 0 0 5px rgba(220, 53, 69, 0.3) !important;
}

.valid-input {
  border-color: #28a745 !important;
  box-shadow: 0 0 5px rgba(40, 167, 69, 0.3) !important;
}

.error-message {
  display: none;
  color: #dc3545;
  font-size: 14px;
  margin-top: 5px;
  font-weight: normal;
}

.success-message {
  display: none;
  color: #28a745;
  font-size: 14px;
  margin-top: 5px;
  font-weight: normal;
}

/* Disclaimer Section */
.disclaimer-section {
  margin: 30px 0;
  animation: slideDown 0.3s ease-out;
}

.disclaimer-box {
  background: #fff3cd;
  border: 2px solid #ffc107;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.disclaimer-box h3 {
  margin-top: 0;
  color: #856404;
}

.disclaimer-box p {
  margin-bottom: 15px;
  color: #856404;
  font-weight: 500;
}

/* Custom Checkbox */
.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: normal;
  margin: 0;
}

.checkbox-container input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #856404;
  border-radius: 3px;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
  background-color: #856404;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
  content: "✓";
  position: absolute;
  top: -2px;
  left: 3px;
  color: white;
  font-weight: bold;
  font-size: 14px;
}

/* Buttons */
.form-actions {
  text-align: center;
  margin-top: 30px;
}

.btn-primary,
.btn-submit {
  padding: 12px 30px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 10px;
}

.btn-primary {
  background-color: #000188;
  color: white;
}

.btn-primary:hover {
  background-color: #000066;
  transform: translateY(-2px);
}

.btn-submit {
  background-color: #28a745;
  color: white;
}

.btn-submit:hover {
  background-color: #218838;
  transform: translateY(-2px);
}

.btn-primary:disabled,
.btn-submit:disabled,
.btn-disabled {
  background-color: #ccc !important;
  cursor: not-allowed !important;
  transform: none !important;
  opacity: 0.6;
}

.btn-primary:disabled:hover,
.btn-submit:disabled:hover,
.btn-disabled:hover {
  background-color: #ccc !important;
  transform: none !important;
}

/* Result Message */
.result-message {
  margin-top: 20px;
  padding: 15px;
  border-radius: 5px;
  text-align: center;
  font-weight: bold;
}

.result-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.result-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Footer Link */
.footer-link {
  display: block;
  text-align: center;
  margin-top: 20px;
  color: #999;
  text-decoration: none;
  font-size: 12px;
}

.footer-link:hover {
  color: #000188;
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design for Footer */
@media (max-width: 1665px) {
  .footer-items {
    max-width: 800px;
  }
  .footer-text {
    max-width: 353px;
  }
}

@media (max-width: 1240px) {
  .footer-inner, .footer-items {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
  .footer-text {
    margin-bottom: 100px;
    color: #ffffff;
    font-size: 22px;
    font-weight: 700;
    position: relative;
    max-width: 495px;
  }
  .footer-text::after {
    content: "";
    display: block;
    border: 1px solid #ffffff;
    width: 353px;
    position: absolute;
    bottom: -65px;
    left: 50%;
    transform: translateX(-50%);
  }
  .footer-contact__title::after {
    width: 176px;
  }
  .footer-item__title {
    margin-bottom: 35px;
  }
  .footer-item {
    margin-bottom: 10px;
  }
  .footer-logo {
    margin-bottom: 40px;
  }
}

@media (max-width: 768px) {
  .footer-bottom__inner {
    flex-direction: column;
    text-align: center;
  }

  .social-share {
    margin-top: 15px;
    flex-direction: column;
  }

  .social-share__title {
    margin-bottom: 10px;
    margin-right: 0;
  }

  .footer-text {
    font-size: 14px;
    line-height: 26px;
    font-weight: 300;
  }
  .footer-item__title {
    font-size: 20px;
  }
  .footer-item__link {
    font-size: 14px;
  }
}

/* Responsive Design for Navbar */
@media (max-width: 1440px) {
  .header-nav__list:not(:last-child) {
    margin-right: 16px;
  }
}

@media (max-width: 1320px) {
  .header-nav__link {
    font-size: 16px;
  }

  .header-btn {
    font-size: 16px;
    padding: 10px 20px;
  }

  .header-logo img {
    width: 110px;
  }
}

@media (max-width: 1024px) {
  .header-nav {
    position: fixed;
    top: 0;
    left: -100%;
    z-index: 20;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    width: 100%;
    height: 100vh;
    transition: left 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
  }

  .header-menuImg {
    display: block;
    position: relative;
    z-index: 21;
    transition: transform 0.3s ease;
  }

  .menu-open .header-menuImg {
    transform: rotate(90deg);
  }

  .header-nav__active {
    left: 0;
  }

  .stop-scroll {
    overflow: hidden;
  }

  .header-nav__list {
    margin: 0 0 40px 0;
  }

  .header-nav__list:not(:last-child) {
    margin-right: 0;
  }
}

/* Responsive Design for Form */
@media (max-width: 768px) {
  .quote-container {
    margin: 20px 10px 10px;
    padding: 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  h1 {
    font-size: 1.8em;
  }

  .subtitle {
    font-size: 1em;
  }

  .btn-primary,
  .btn-submit {
    display: block;
    width: 100%;
    margin: 10px 0;
  }
}

@media (max-width: 480px) {
  body {
    padding: 10px;
  }
  
  .quote-container {
    padding: 15px;
  }
  
  h1 {
    font-size: 1.5em;
  }
  
  input[type="text"],
  input[type="number"],
  input[type="email"],
  select,
  textarea {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Footer Styles */
.footer {
  background-color: #01184A;
  padding-top: 45px;
  margin-top: 40px;
}

.footer-inner {
  padding-bottom: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.footer-bottom {
  background-color: #7B8BA7;
  padding: 30px 20px;
  margin: 0 auto;
}

.footer-bottom__inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  padding: 15px 0;
}

.footer-bottom__text {
  font-weight: 400;
  font-size: 18px;
  color: #FFFFFF;
  text-align: center;
  font-family: 'Open Sans', sans-serif;
}

.footer-logo {
  margin-bottom: 20px;
}

.footer-logo img {
  width: 190px;
}

.footer-text {
  max-width: 494px;
  font-size: 18px;
  line-height: 26px;
  color: #FFFFFF;
  margin-bottom: 45px;
}

.footer-items {
  display: flex;
  max-width: 995px;
  width: 100%;
  justify-content: space-between;
}

.footer-item__title {
  margin-bottom: 30px;
  color: #ffffff;
  font-size: 22px;
  font-weight: 700;
  position: relative;
}

.footer-item__title::after {
  content: "";
  display: block;
  border: 1px solid #ffffff;
  width: 176px;
  position: absolute;
  bottom: -10px;
  left: 51%;
  transform: translateX(-50%);
}

.footer-item__list {
  margin-bottom: 25px;
}

.footer-item__link {
  font-weight: 300;
  font-size: 18px;
  color: #ffffff;
  display: block;
  transition: all 0.5s ease-in-out;
}

.footer-item__link:hover {
  transform: scale(1.1);
}

.footer-contact__title {
  margin-bottom: 30px;
  color: #ffffff;
  font-size: 22px;
  font-weight: 700;
  position: relative;
}

.footer-contact {
  display: flex;
  flex-direction: column;
}

.footer-contact__title::after {
  content: "";
  display: block;
  border: 1px solid #ffffff;
  width: 292px;
  position: absolute;
  bottom: -10px;
  left: 53%;
  transform: translateX(-50%);
}

.footer-contact__link {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.footer-contact__link img {
  width: 20px;
  margin-right: 20px;
}

.footer-contact__link span {
  font-size: 18px;
  font-weight: 300;
  color: #ffffff;
}

/* Social Media Styles */
.social-share {
  display: flex;
  align-items: center;
  gap: 15px;
}

.social-share__title {
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  margin-right: 10px;
}

.social-share__links {
  display: flex;
  gap: 10px;
}

.social-share__link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #FFFFFF;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.social-share__link:hover {
  background-color: #00FF00;
  transform: translateY(-2px);
}

.social-share__link svg {
  width: 20px;
  height: 20px;
  fill: #01184A;
}
