/* Reset and Base Styles */
*,
*::after,
*::before {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Outfit', sans-serif;
  background-color: #f4f4f4;
  line-height: 1.6;
  color: #333;
}

a {
  text-decoration: none;
}

ul, li {
  list-style: none;
  margin: 0;
  padding: 0;
}

/* Main site container for navbar */
.container {
  margin: 0 auto;
  max-width: 1680px;
  padding: 0 20px;
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #000F47;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 999;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  background-color: #00FF00;
  color: #000F47;
  transform: translateY(-3px);
}

/* Header and Navigation Styles */
.header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  padding: 20px 0;
}

.header-items {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-logo img {
  width: 140px;
}

.header-menuImg {
  display: none;
  cursor: pointer;
}

.header-nav {
  display: flex;
  align-items: center;
  transition: left 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
}

.header-nav__list {
  margin-right: 40px;
}

.header-nav__list:last-child {
  margin-right: 0;
}

.header-nav__link {
  font-size: 20px;
  font-weight: 400;
  color: #000E49;
  transition: all 0.5s ease-in-out;
  display: inline-block;
}

.header-nav__link:hover {
  transform: scale(1.1);
}

.header-btn {
  font-size: 20px;
  font-weight: 800;
  color: #FFFFFF;
  padding: 15px 35px;
  background-color: #000F47;
  transition: all 0.5s ease-in-out;
}

.header-btn:hover {
  transition: all 0.5s ease-in-out;
  opacity: 0.8;
}

/* Quote form container - renamed to avoid conflict with main site container */
.quote-container {
  max-width: 900px;
  margin: 40px auto 20px;
  background: #fff;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.quote-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #000188, #00FF00);
}

/* Typography */
h1 {
  margin-top: 0;
  margin-bottom: 8px;
  color: #000188;
  font-size: 2.5em;
  text-align: center;
  font-weight: 700;
  letter-spacing: -0.02em;
}

.subtitle {
  text-align: center;
  color: #6b7280;
  margin-bottom: 40px;
  font-size: 1.125em;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

h2 {
  color: #000188;
  border: none;
  padding: 16px 0 12px;
  margin-top: 40px;
  margin-bottom: 24px;
  font-size: 1.5em;
  font-weight: 600;
  position: relative;
  display: flex;
  align-items: center;
}

h2::before {
  content: '';
  width: 4px;
  height: 24px;
  background: linear-gradient(180deg, #000188, #00FF00);
  margin-right: 12px;
  border-radius: 2px;
}

h2::after {
  content: '';
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, rgba(0, 1, 136, 0.2), transparent);
  margin-left: 16px;
}

h3 {
  color: #000188;
  margin-bottom: 12px;
  font-weight: 600;
}

/* Form Sections */
.form-section {
  margin-bottom: 40px;
  padding: 24px;
  background: #fafbfc;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.form-section:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
}

.form-row {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
}

.form-col {
  flex: 1;
  min-width: 0;
}

/* Form Elements */
label {
  display: block;
  margin: 0 0 8px;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  letter-spacing: 0.025em;
  position: relative;
}

label[for*="*"]::after,
label:has(+ input[required])::after,
label:has(+ select[required])::after,
label:has(+ textarea[required])::after {
  content: '*';
  color: #ef4444;
  margin-left: 4px;
  font-weight: 500;
}

.form-field {
  position: relative;
  margin-bottom: 20px;
}

input[type="text"],
input[type="number"],
input[type="email"],
select,
textarea {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  font-family: inherit;
  background-color: #fff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: #000188;
  box-shadow: 0 0 0 3px rgba(0, 1, 136, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

input[type="text"]:hover:not(:focus),
input[type="number"]:hover:not(:focus),
input[type="email"]:hover:not(:focus),
select:hover:not(:focus),
textarea:hover:not(:focus) {
  border-color: #d1d5db;
}

textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.5;
}

select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}

/* Placeholder styling */
input::placeholder,
textarea::placeholder {
  color: #9ca3af;
  opacity: 1;
}

/* Input states */
input:disabled,
select:disabled,
textarea:disabled {
  background-color: #f9fafb;
  border-color: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
}

/* Validation Styles */
.error-input {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
  background-color: #fef2f2 !important;
}

.error-input:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2), 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.valid-input {
  border-color: #10b981 !important;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
  background-color: #f0fdf4 !important;
}

.valid-input:focus {
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2), 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.error-message {
  display: none;
  color: #ef4444;
  font-size: 13px;
  margin-top: 6px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.error-message::before {
  content: '⚠';
  font-size: 14px;
}

.success-message {
  display: none;
  color: #10b981;
  font-size: 13px;
  margin-top: 6px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.success-message::before {
  content: '✓';
  font-size: 14px;
  font-weight: bold;
}

/* Field validation icons */
.form-field {
  position: relative;
}

.form-field.has-error input,
.form-field.has-error select,
.form-field.has-error textarea {
  padding-right: 44px;
}

.form-field.has-success input,
.form-field.has-success select,
.form-field.has-success textarea {
  padding-right: 44px;
}

.form-field.has-error::after {
  content: '⚠';
  position: absolute;
  right: 14px;
  top: 50%;
  transform: translateY(-50%);
  color: #ef4444;
  font-size: 16px;
  pointer-events: none;
}

.form-field.has-success::after {
  content: '✓';
  position: absolute;
  right: 14px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-size: 16px;
  font-weight: bold;
  pointer-events: none;
}

/* Disclaimer Section */
.disclaimer-section {
  margin: 40px 0;
  animation: slideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.disclaimer-box {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 2px solid #f59e0b;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  position: relative;
  overflow: hidden;
}

.disclaimer-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: #f59e0b;
}

.disclaimer-box h3 {
  margin-top: 0;
  color: #92400e;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.disclaimer-box h3::before {
  content: '⚠';
  font-size: 18px;
  color: #f59e0b;
}

.disclaimer-box p {
  margin-bottom: 16px;
  color: #92400e;
  font-weight: 500;
  line-height: 1.6;
}

/* Custom Checkbox */
.checkbox-container {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  font-weight: 500;
  margin: 0;
  padding: 16px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  transition: all 0.3s ease;
  gap: 12px;
  color: #92400e;
}

.checkbox-container:hover {
  background: rgba(255, 255, 255, 0.7);
}

.checkbox-container input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #f59e0b;
  border-radius: 4px;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  margin-top: 2px;
  background: #fff;
}

.checkbox-container:hover .checkmark {
  border-color: #d97706;
  transform: scale(1.05);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
  background-color: #f59e0b;
  border-color: #f59e0b;
  transform: scale(1.1);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
  content: "✓";
  position: absolute;
  top: -1px;
  left: 3px;
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.checkbox-container input[type="checkbox"]:focus + .checkmark {
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2);
}

/* Buttons */
.form-actions {
  text-align: center;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid #e5e7eb;
}

.btn-primary,
.btn-submit {
  padding: 16px 32px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin: 0 8px;
  position: relative;
  overflow: hidden;
  min-width: 160px;
  font-family: inherit;
  letter-spacing: 0.025em;
}

.btn-primary {
  background: linear-gradient(135deg, #000188 0%, #000066 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 1, 136, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #000066 0%, #000044 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 1, 136, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 1, 136, 0.3);
}

.btn-submit {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-submit:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
}

.btn-submit:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

/* Loading state */
.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  color: white;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Disabled state */
.btn-primary:disabled,
.btn-submit:disabled,
.btn-disabled {
  background: #9ca3af !important;
  cursor: not-allowed !important;
  transform: none !important;
  opacity: 0.6;
  box-shadow: none !important;
}

.btn-primary:disabled:hover,
.btn-submit:disabled:hover,
.btn-disabled:hover {
  background: #9ca3af !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Result Message */
.result-message {
  margin-top: 24px;
  padding: 20px 24px;
  border-radius: 12px;
  text-align: center;
  font-weight: 600;
  font-size: 16px;
  position: relative;
  animation: slideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.result-message.success {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: #065f46;
  border: 2px solid #10b981;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

.result-message.success::before {
  content: '✓';
  font-size: 20px;
  font-weight: bold;
  color: #10b981;
  background: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.result-message.error {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #991b1b;
  border: 2px solid #ef4444;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

.result-message.error::before {
  content: '⚠';
  font-size: 20px;
  font-weight: bold;
  color: #ef4444;
  background: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

/* Footer Link */
.footer-link {
  display: block;
  text-align: center;
  margin-top: 20px;
  color: #999;
  text-decoration: none;
  font-size: 12px;
}

.footer-link:hover {
  color: #000188;
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design for Footer */
@media (max-width: 1665px) {
  .footer-items {
    max-width: 800px;
  }
  .footer-text {
    max-width: 353px;
  }
}

@media (max-width: 1240px) {
  .footer-inner, .footer-items {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
  .footer-text {
    margin-bottom: 100px;
    color: #ffffff;
    font-size: 22px;
    font-weight: 700;
    position: relative;
    max-width: 495px;
  }
  .footer-text::after {
    content: "";
    display: block;
    border: 1px solid #ffffff;
    width: 353px;
    position: absolute;
    bottom: -65px;
    left: 50%;
    transform: translateX(-50%);
  }
  .footer-contact__title::after {
    width: 176px;
  }
  .footer-item__title {
    margin-bottom: 35px;
  }
  .footer-item {
    margin-bottom: 10px;
  }
  .footer-logo {
    margin-bottom: 40px;
  }
}

@media (max-width: 768px) {
  .footer-bottom__inner {
    flex-direction: column;
    text-align: center;
  }

  .social-share {
    margin-top: 15px;
    flex-direction: column;
  }

  .social-share__title {
    margin-bottom: 10px;
    margin-right: 0;
  }

  .footer-text {
    font-size: 14px;
    line-height: 26px;
    font-weight: 300;
  }
  .footer-item__title {
    font-size: 20px;
  }
  .footer-item__link {
    font-size: 14px;
  }
}

/* Responsive Design for Navbar */
@media (max-width: 1440px) {
  .header-nav__list:not(:last-child) {
    margin-right: 16px;
  }
}

@media (max-width: 1320px) {
  .header-nav__link {
    font-size: 16px;
  }

  .header-btn {
    font-size: 16px;
    padding: 10px 20px;
  }

  .header-logo img {
    width: 110px;
  }
}

@media (max-width: 1024px) {
  .header-nav {
    position: fixed;
    top: 0;
    left: -100%;
    z-index: 20;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    width: 100%;
    height: 100vh;
    transition: left 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
  }

  .header-menuImg {
    display: block;
    position: relative;
    z-index: 21;
    transition: transform 0.3s ease;
  }

  .menu-open .header-menuImg {
    transform: rotate(90deg);
  }

  .header-nav__active {
    left: 0;
  }

  .stop-scroll {
    overflow: hidden;
  }

  .header-nav__list {
    margin: 0 0 40px 0;
  }

  .header-nav__list:not(:last-child) {
    margin-right: 0;
  }
}

/* Responsive Design for Form */
@media (max-width: 768px) {
  .quote-container {
    margin: 20px 16px 10px;
    padding: 24px 20px;
    border-radius: 12px;
  }

  .form-section {
    padding: 20px;
    margin-bottom: 32px;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .form-col {
    margin-bottom: 16px;
  }

  h1 {
    font-size: 2em;
  }

  h2 {
    font-size: 1.3em;
    margin-top: 32px;
  }

  .subtitle {
    font-size: 1em;
    margin-bottom: 32px;
  }

  .btn-primary,
  .btn-submit {
    display: block;
    width: 100%;
    margin: 8px 0;
    padding: 16px;
  }

  .form-actions {
    margin-top: 32px;
    padding-top: 24px;
  }

  .disclaimer-box {
    padding: 20px;
  }

  .checkbox-container {
    padding: 12px;
    font-size: 14px;
  }

  /* Wizard responsive styles */
  .wizard-progress {
    margin-bottom: 32px;
    padding: 20px 0;
  }

  .progress-steps {
    margin-bottom: 20px;
  }

  .step-title {
    font-size: 12px;
    max-width: 100px;
  }

  .step-number {
    width: 28px;
    height: 28px;
    font-size: 13px;
    margin-bottom: 6px;
  }

  .wizard-navigation {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
  }

  .btn-wizard {
    width: 100%;
    padding: 14px 20px;
    min-width: auto;
  }

  .progress-step:not(:last-child)::after {
    top: 14px;
  }

  /* Review section mobile styles */
  .review-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .review-label {
    min-width: auto;
  }

  .review-value {
    text-align: left;
    margin-left: 0;
  }

  .review-section {
    padding: 20px 16px;
  }

  .review-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .review-category {
    padding: 16px;
  }

  .review-actions-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  body {
    padding: 8px;
  }

  .quote-container {
    margin: 16px 8px 8px;
    padding: 20px 16px;
  }

  .form-section {
    padding: 16px;
  }

  h1 {
    font-size: 1.75em;
  }

  h2 {
    font-size: 1.2em;
  }

  .subtitle {
    font-size: 0.95em;
  }

  input[type="text"],
  input[type="number"],
  input[type="email"],
  select,
  textarea {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 14px;
  }

  .checkmark {
    width: 18px;
    height: 18px;
  }

  .disclaimer-box {
    padding: 16px;
  }

  .result-message {
    padding: 16px 20px;
    font-size: 14px;
  }

  .result-message.success::before,
  .result-message.error::before {
    width: 28px;
    height: 28px;
    font-size: 16px;
  }

  /* Wizard mobile styles */
  .wizard-progress {
    padding: 16px 0;
    margin-bottom: 24px;
  }

  .step-title {
    font-size: 11px;
    max-width: 80px;
  }

  .step-number {
    width: 24px;
    height: 24px;
    font-size: 12px;
    margin-bottom: 4px;
  }

  .progress-step:not(:last-child)::after {
    top: 12px;
  }

  .btn-wizard {
    padding: 12px 16px;
    font-size: 13px;
  }

  .wizard-navigation {
    gap: 10px;
    margin-bottom: 16px;
    padding-top: 16px;
  }
}

/* Enhanced focus styles for better accessibility */
@media (prefers-reduced-motion: no-preference) {
  .form-section {
    transition: all 0.3s ease;
  }

  input, select, textarea, button {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .form-section {
    border-color: #000;
    border-width: 2px;
  }

  input, select, textarea {
    border-color: #000;
    border-width: 2px;
  }

  .btn-primary, .btn-submit {
    border: 2px solid #000;
  }
}

/* Footer Styles */
.footer {
  background-color: #01184A;
  padding-top: 45px;
  margin-top: 40px;
}

.footer-inner {
  padding-bottom: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.footer-bottom {
  background-color: #7B8BA7;
  padding: 30px 20px;
  margin: 0 auto;
}

.footer-bottom__inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  padding: 15px 0;
}

.footer-bottom__text {
  font-weight: 400;
  font-size: 18px;
  color: #FFFFFF;
  text-align: center;
  font-family: 'Open Sans', sans-serif;
}

.footer-logo {
  margin-bottom: 20px;
}

.footer-logo img {
  width: 190px;
}

.footer-text {
  max-width: 494px;
  font-size: 18px;
  line-height: 26px;
  color: #FFFFFF;
  margin-bottom: 45px;
}

.footer-items {
  display: flex;
  max-width: 995px;
  width: 100%;
  justify-content: space-between;
}

.footer-item__title {
  margin-bottom: 30px;
  color: #ffffff;
  font-size: 22px;
  font-weight: 700;
  position: relative;
}

.footer-item__title::after {
  content: "";
  display: block;
  border: 1px solid #ffffff;
  width: 176px;
  position: absolute;
  bottom: -10px;
  left: 51%;
  transform: translateX(-50%);
}

.footer-item__list {
  margin-bottom: 25px;
}

.footer-item__link {
  font-weight: 300;
  font-size: 18px;
  color: #ffffff;
  display: block;
  transition: all 0.5s ease-in-out;
}

.footer-item__link:hover {
  transform: scale(1.1);
}

.footer-contact__title {
  margin-bottom: 30px;
  color: #ffffff;
  font-size: 22px;
  font-weight: 700;
  position: relative;
}

.footer-contact {
  display: flex;
  flex-direction: column;
}

.footer-contact__title::after {
  content: "";
  display: block;
  border: 1px solid #ffffff;
  width: 292px;
  position: absolute;
  bottom: -10px;
  left: 53%;
  transform: translateX(-50%);
}

.footer-contact__link {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.footer-contact__link img {
  width: 20px;
  margin-right: 20px;
}

.footer-contact__link span {
  font-size: 18px;
  font-weight: 300;
  color: #ffffff;
}

/* Social Media Styles */
.social-share {
  display: flex;
  align-items: center;
  gap: 15px;
}

.social-share__title {
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  margin-right: 10px;
}

.social-share__links {
  display: flex;
  gap: 10px;
}

.social-share__link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #FFFFFF;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.social-share__link:hover {
  background-color: #00FF00;
  transform: translateY(-2px);
}

.social-share__link svg {
  width: 20px;
  height: 20px;
  fill: #01184A;
}

/* Form Enhancement Utilities */
.form-progress {
  display: flex;
  justify-content: space-between;
  margin-bottom: 32px;
  padding: 0 8px;
}

.progress-step {
  flex: 1;
  text-align: center;
  position: relative;
  color: #9ca3af;
  font-size: 14px;
  font-weight: 500;
}

.progress-step.active {
  color: #000188;
  font-weight: 600;
}

.progress-step.completed {
  color: #10b981;
}

.progress-step::before {
  content: '';
  display: block;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e5e7eb;
  margin: 0 auto 8px;
  position: relative;
  transition: all 0.3s ease;
}

.progress-step.active::before {
  background: #000188;
  box-shadow: 0 0 0 4px rgba(0, 1, 136, 0.2);
}

.progress-step.completed::before {
  background: #10b981;
}

.progress-step.completed::after {
  content: '✓';
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  font-size: 14px;
  font-weight: bold;
}

/* Floating labels effect */
.floating-label {
  position: relative;
  margin-bottom: 24px;
}

.floating-label input,
.floating-label select,
.floating-label textarea {
  padding-top: 20px;
  padding-bottom: 12px;
}

.floating-label label {
  position: absolute;
  top: 16px;
  left: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  color: #9ca3af;
  font-weight: 400;
}

.floating-label input:focus + label,
.floating-label input:not(:placeholder-shown) + label,
.floating-label select:focus + label,
.floating-label select:not([value=""]) + label,
.floating-label textarea:focus + label,
.floating-label textarea:not(:placeholder-shown) + label {
  top: 4px;
  font-size: 12px;
  color: #000188;
  font-weight: 600;
}

/* Tooltip styles */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 200px;
  background-color: #374151;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 8px 12px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 14px;
  font-weight: 400;
}

.tooltip .tooltiptext::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #374151 transparent transparent transparent;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

/* Form field groups */
.field-group {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.field-group-title {
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  font-size: 16px;
}

/* Loading overlay */
.form-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #000188;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Multi-step Wizard Styles */
.wizard-progress {
  margin-bottom: 40px;
  padding: 24px 0;
  border-bottom: 1px solid #e5e7eb;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  position: relative;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
  z-index: 2;
}

.progress-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 16px;
  left: 50%;
  right: -50%;
  height: 2px;
  background: #e5e7eb;
  z-index: 1;
}

.progress-step.completed:not(:last-child)::after {
  background: #10b981;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e5e7eb;
  color: #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 3;
}

.progress-step.active .step-number {
  background: #000188;
  color: white;
  box-shadow: 0 0 0 4px rgba(0, 1, 136, 0.2);
}

.progress-step.completed .step-number {
  background: #10b981;
  color: white;
}

.progress-step.completed .step-number::after {
  content: '✓';
  position: absolute;
  font-size: 12px;
  font-weight: bold;
}

.step-title {
  font-size: 13px;
  font-weight: 500;
  color: #9ca3af;
  text-align: center;
  max-width: 120px;
  line-height: 1.3;
  transition: color 0.3s ease;
}

.progress-step.active .step-title {
  color: #000188;
  font-weight: 600;
}

.progress-step.completed .step-title {
  color: #10b981;
}

.progress-bar {
  position: relative;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #000188, #10b981);
  border-radius: 2px;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  width: 0%;
}

/* Wizard Navigation */
.wizard-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  padding: 24px 0;
  border-top: 1px solid #e5e7eb;
}

.btn-wizard {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 120px;
  justify-content: center;
}

.btn-wizard:hover:not(:disabled) {
  border-color: #000188;
  color: #000188;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 1, 136, 0.15);
}

.btn-wizard:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.btn-next {
  background: linear-gradient(135deg, #000188 0%, #000066 100%);
  color: white;
  border-color: #000188;
}

.btn-next:hover:not(:disabled) {
  background: linear-gradient(135deg, #000066 0%, #000044 100%);
  border-color: #000066;
  color: white;
}

.btn-next:disabled {
  background: #9ca3af !important;
  border-color: #9ca3af !important;
  color: white !important;
}

.btn-prev svg {
  order: -1;
}

/* Form section transitions */
.form-section {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.form-section.wizard-hidden {
  opacity: 0;
  transform: translateX(-30px);
  pointer-events: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

.form-section.wizard-visible {
  opacity: 1;
  transform: translateX(0);
  pointer-events: auto;
  position: relative;
  z-index: 2;
}

/* Form content wrapper */
.form-content {
  position: relative;
}

/* Review Step Styles */
.review-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 32px;
}

.review-section h3 {
  color: #000188;
  margin-top: 0;
  margin-bottom: 24px;
  font-size: 1.3em;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  text-align: center;
  justify-content: center;
}

.review-section h3::before {
  content: '📋';
  font-size: 1.2em;
}

.review-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.review-category {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.review-category h4 {
  color: #000188;
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.1em;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.review-category h4::before {
  content: '';
  width: 3px;
  height: 16px;
  background: linear-gradient(180deg, #000188, #00FF00);
  border-radius: 2px;
}

.review-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #e5e7eb;
}

.review-item:last-child {
  border-bottom: none;
}

.review-label {
  font-weight: 600;
  color: #374151;
  min-width: 140px;
  flex-shrink: 0;
}

.review-value {
  color: #6b7280;
  text-align: right;
  flex: 1;
  margin-left: 16px;
  word-break: break-word;
}

.review-value.empty {
  color: #9ca3af;
  font-style: italic;
}

.review-actions {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
  margin-top: 24px;
}

.review-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 24px;
}

.btn-edit {
  background: transparent;
  border: 1px solid #d1d5db;
  color: #6b7280;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  font-weight: 500;
}

.btn-edit:hover {
  border-color: #000188;
  color: #000188;
  background: rgba(0, 1, 136, 0.05);
  transform: translateY(-1px);
}
